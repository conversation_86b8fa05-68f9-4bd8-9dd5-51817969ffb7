<?php

/**
 * 使用AES算法对content加密 (Encrypts content using the AES algorithm)
 *
 * 这个函数是为了兼容一个特定的Java加密实现而创建的。
 * This function was created to be compatible with a specific Java encryption implementation.
 *
 * Java实现细节 (Java implementation details):
 * - KeyGenerator: AES
 * - SecureRandom: SHA1PRNG, seeded with the encryptKey.
 * - Key Size: 128 bits.
 * - Cipher: AES/ECB/PKCS5Padding (default for "AES").
 *
 * @param string $content    待加密的内容 (The content to be encrypted).
 * @param string $encryptKey 加密密钥 (The encryption key).
 * @return string 加密后的16进制字符串 (The encrypted content as a hex string).
 * @throws RuntimeException 如果加密失败 (if encryption fails).
 */
function encrypt($content, $encryptKey) {
    // Java的KeyGenerator与一个带种子的SecureRandom ("SHA1PRNG") 结合使用，这不是一个标准的密钥派生函数（KDF）。
    // 它从输入的密钥字符串确定性地生成一个128位（16字节）的密钥。
    // 在PHP中，一个常见的、能达到类似确定性结果的方法是哈希输入密钥。
    // 我们使用SHA1（源于"SHA1PRNG"名称），并取其结果的前16个字节作为128位密钥。
    //
    // Java's KeyGenerator with a seeded SecureRandom ("SHA1PRNG") is not a standard Key Derivation Function (KDF).
    // It deterministically generates a 128-bit (16-byte) key from the input key string.
    // A common way to achieve a similar deterministic result in PHP is to hash the input key.
    // We'll use SHA1 (from the "SHA1PRNG" name) and take the first 16 bytes of its raw output for a 128-bit key.
    $key = substr(sha1($encryptKey, true), 0, 16);

    // Java中的 Cipher.getInstance("AES") 通常默认为 "AES/ECB/PKCS5Padding"。
    // 我们使用 'aes-128-ecb'，这是与之等效的算法。
    // OpenSSL默认处理PKCS7填充，它与AES的PKCS5Padding兼容。
    //
    // The Java code Cipher.getInstance("AES") often defaults to "AES/ECB/PKCS5Padding".
    // We will use 'aes-128-ecb', which is the equivalent.
    // OpenSSL handles PKCS7 padding by default, which is compatible with PKCS5Padding for AES.
    $encrypted = openssl_encrypt($content, 'aes-128-ecb', $key, OPENSSL_RAW_DATA);

    if ($encrypted === false) {
        // 如果加密失败，抛出异常并附带OpenSSL错误信息。
        // If encryption fails, throw an exception with the OpenSSL error message.
        throw new RuntimeException('Encryption failed: ' . openssl_error_string());
    }

    // 将加密后的原始二进制数据转换为16进制字符串，以匹配Java的byteArr2HexStr方法。
    // Convert the raw encrypted bytes to a hex string to match Java's byteArr2HexStr method.
    return bin2hex($encrypted);
}

// --- 使用示例 (Example Usage) ---

$contentToEncrypt = '[{"orderId":3729851333396276480,"uid":40993059866,"orderTime":1753863885000,"payTime":1753863885000,"amount":1,"price":99.00,"status":2,"refundNo":"","refundTime":0}]';
$secretKey = "2061A21ED53C4A4594C27946A21E2A88";

try {
    $encryptedHex = encrypt($contentToEncrypt, $secretKey);

    echo "Original Content: " . $contentToEncrypt . "\n";
    echo "Secret Key: " . $secretKey . "\n";
    echo "Encrypted (Hex): " . $encryptedHex . "\n";
    // 预期的Java输出 (Expected Java output): 2916284822c595a467769a41d9534165

} catch (RuntimeException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}


?>